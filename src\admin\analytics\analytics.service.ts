import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { PgService } from 'src/database/pg/pg.service';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class AnalyticsService {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
  ) {}

  //#region getAprWiseAnalytics
  async getAprWiseAnalytics(query) {
    // const month = query?.month;
    let startDate = new Date();
    startDate.setDate(1);
    let endDate: Date;
    if (month || month === 0) {
      if (month > startDate.getMonth()) {
        startDate.setFullYear(startDate.getFullYear() - 1);
      }
      startDate.setMonth(month);
    }
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    startDate = this.dateService.getGlobalDate(startDate);
    endDate = this.dateService.getGlobalDate(endDate);
    const { loanData, emiData } = await this.getEmiAndLoanData({
      startDate,
      endDate,
    });

    const loanAprs = this.aprs(loanData, false);
    const emiAprs = this.aprs(emiData, true);

    const reqData = {
      loanAprs,
      emiAprs,
      totalLoans: loanData?.length,
      totalEmis: emiData?.length,
      binSize: query?.binSize || 10,
    };
    const { loanBins, emiBins } = this.createBins(reqData);

    return {
      totalLoans: loanData?.length,
      totalEmis: emiData?.length,
      loanBins: loanBins?.filter((bin) => bin.loanCount > 0),
      emiBins: emiBins?.filter((bin) => bin.emiCount > 0),
      // binSize: query?.binSize || 10,
    };
  }
  //#endregion

  //#region getEmiAndLoanData
  async getEmiAndLoanData(data) {
    const { startDate, endDate } = data;
    const [loanData, emiRawData] = await Promise.all([
      this.pg.findAll(loanTransaction, {
        attributes: [
          'id',
          'interestRate',
          'netApprovedAmount',
          'loanDisbursementDateTime',
        ],
        where: {
          loanDisbursementDateTime: { [Op.gte]: startDate, [Op.lt]: endDate },
        },
        raw: true,
      }),
      this.pg.findAll(EmiEntity, {
        attributes: [
          'loanId',
          'emi_date',
          'payment_done_date',
          'payment_status',
        ],
        include: [
          {
            model: loanTransaction,
            attributes: ['interestRate'],
            required: true,
          },
        ],
        where: { emi_date: { [Op.gte]: startDate, [Op.lt]: endDate } },
        raw: false,
      }),
    ]);

    const paidEmiData = emiRawData.filter(
      (emi) => emi.payment_done_date !== null,
    );
    return { loanData, emiData: paidEmiData };
  }
  //#endregion

  private aprs(data, isEmi = false) {
    const annualPercentageRates = data.map((record) => {
      const interestRate = isEmi
        ? (record?.loan?.interestRate ?? record?.loanTransaction?.interestRate)
        : record?.interestRate;
      if (!interestRate) return 0;
      return interestRate * 365;
    });
    const aprs = annualPercentageRates.filter((apr) => !isNaN(apr) && apr > 0);
    return aprs;
  }

  //#region createBins
  private createBins(reqData) {
    const loanAprs = reqData?.loanAprs;
    const emiAprs = reqData?.emiAprs;
    const totalLoans = reqData?.totalLoans;
    const totalEmis = reqData?.totalEmis;
    const binSize = reqData?.binSize;
    const allAprs = [...loanAprs, ...emiAprs];
    if (!allAprs.length) return { loanBins: [], emiBins: [] };

    const minApr = Math.min(...allAprs);
    const maxApr = Math.max(...allAprs);
    const startBin = Math.floor(minApr / binSize) * binSize;
    const endBin = Math.ceil(maxApr / binSize) * binSize;

    const loanBins = [];
    const emiBins = [];

    for (let i = startBin; i < endBin; i += binSize) {
      const binMax = i + binSize - 1;
      const loanCount = loanAprs.filter(
        (apr) => apr >= i && apr <= binMax,
      ).length;
      const emiCount = emiAprs.filter(
        (apr) => apr >= i && apr <= binMax,
      ).length;

      loanBins.push({
        aprRange: `${i}-${binMax}`,
        loanCount,
        loanPercent: totalLoans
          ? +((loanCount / totalLoans) * 100).toFixed(2)
          : 0,
      });

      emiBins.push({
        aprRange: `${i}-${binMax}`,
        emiCount,
        emiPercent: totalEmis ? +((emiCount / totalEmis) * 100).toFixed(2) : 0,
      });
    }

    return { loanBins, emiBins };
  }
}
//#endregion
